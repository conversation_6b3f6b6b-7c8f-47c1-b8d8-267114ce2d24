import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../views/seeker/layouts/layout_active_order_details.dart';
import '../../views/seeker/layouts/layout_employee_bio.dart';
import '../../views/seeker/layouts/layout_order_chats.dart';
import '../../views/seeker/layouts/layout_order_details.dart';

class HomeController extends GetxController {
  var offerLayouts = [LayoutActiveOrderDetails(), LayoutEmployeeBio()];

  var orderChatLayouts = [LayoutOrderDetails(), LayoutOrderChats()];

  /// dropdown btn
  RxString selectedValue = "List view".obs;

  /// dropdown items list
  List<String> dropdownValues = ["List view", "Map view"];

  /// another dropdown
  RxString selectedCountry = "Pakistan".obs;

  /// dropdown items list
  List<String> countryList = ["Pakistan", "Turkey", "Palestine", "China"];

  /// dropdown btn
  RxString selectedCity = "Islamabad".obs;

  /// dropdown items list
  List<String> cityList = ["Islamabad", "Lahore"];

  /// another dropdown
  RxString selectedCategory = "UIUX".obs;

  /// dropdown items list
  List<String> categoryList = ["UIUX", "Figma", "Flutter", "Android"];

  /// add more items
  RxList<TextFormField> addItem = <TextFormField>[TextFormField()].obs;

  /// date picker
  final Rx<DateTime> selectedDate = Rx<DateTime>(DateTime.now());

  void addMoreItem() {
    addItem.add(TextFormField());
  }

  Future<void> selectDate(BuildContext context) async {
    DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
    );

    if (pickedDate != null && pickedDate != selectedDate.value) {
      selectedDate.value = pickedDate;
    }
  }
}
