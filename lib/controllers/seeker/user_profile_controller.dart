import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:i_squares/utils/api_constants.dart';
import 'package:i_squares/utils/show_message.dart';

import '../../utils/dio_const.dart';

class UserProfileController extends GetxController {
  Rx<Map<String, dynamic>> user = Rx({});

  @override
  void onInit() {
    super.onInit();
    fetchCurrentUser();
  }

  Future<(String, dynamic)> updateUserMode(String type) async {
    var response = await dio.patch(
      ApiConstants.SWITCH_TYPE_URL,
      queryParameters: {"user_type": type},
    );
    if (response.statusCode == 200) {
      return ('success', response.data);
    } else {
      return (
        (response.data['detail'] ??
                response.data['message'] ??
                'Something went wrong')
            .toString(),
        null,
      );
    }
  }

  Future<void> fetchCurrentUser() async {
    var id = GetStorage().read('auth')?['user']?['id'];
    if (id == null) {
      return;
    }
    var response = await dio.get(
      ApiConstants.GET_USER_PROFILE_URL(id.toString()),
    );
    if (response.statusCode == 200) {
      user.value = Map<String, dynamic>.from(response.data);
    } else {
      showMessage("Something went wrong while fetching user profile");
    }
  }
}
