import 'package:get/get.dart';
import 'package:i_squares/utils/api_constants.dart';
import 'package:i_squares/utils/dio_const.dart';
import 'package:i_squares/widgets/paginated_list_view.dart';

class OrdersController extends GetxController {
  // Selected filter for orders
  var selectedOrderStatus = 'all'.obs;

  // Fetch orders with pagination
  Future<List<Map<String, dynamic>>> fetchOrders({
    required int offset,
    required int limit,
    String? status,
  }) async {
    try {
      final response = await dio.get(
        ApiConstants.GET_ORDERS_URL,
        queryParameters: {
          'offset': offset,
          'limit': limit,
          if (status != null && status != 'all') 'status': status,
        },
      );

      if (response.statusCode == 200) {
        return (response.data as List? ?? [])
            .map((e) => Map<String, dynamic>.from(e))
            .toList();
      }

      return [];
    } catch (e) {
      print('Error fetching orders: $e');
      return [];
    }
  }

  // Refresh orders when filter changes
  void refreshOrders() {
    final controller = Get.find<PaginationController>(
      tag: "provider_orders_${selectedOrderStatus.value}",
    );
    controller.restart();
  }
}
