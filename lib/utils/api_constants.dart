import 'dart:io';

import 'package:flutter/foundation.dart';

class ApiConstants {
  /// <--- Common  --->
  static final String GET_CATEGORIES_URL = BASE_URL + "/categories";

  static final String GET_QUIZ_QUESTIONS_URL = BASE_URL + "/quiz-questions";
  static final String GET_USERS_URL = BASE_URL + "/users";
  static final String SWITCH_TYPE_URL = BASE_URL + "/switch-type";
  static final String GET_JOB_DETAILS_URL = BASE_URL + "/job-details";
  static final String GET_JOB_PARTIES_URL = BASE_URL + "/job-parties";
  static final String GET_ORDERS_URL = BASE_URL + "/orders";

  ///  <--- Auth --->
  static final String SIGNUP_URL = BASE_URL + "/auth/signup";
  static final String LOGIN_URL = BASE_URL + "/auth/login";

  static final String COMPLETE_USER_PROFILE =
      BASE_URL + "/profile/complete-profile";
  static final String REFRESH_TOKEN_URL = BASE_URL + "/auth/refresh-token";
  static final String SEND_VERIFICATION_EMAIL_URL =
      BASE_URL + "/auth/send-verification-email";
  static final String REQUEST_RESET_PASSWORD_URL =
      BASE_URL + "/auth/request-reset-password";
  static final String VERIFY_RESET_PASSWORD_URL =
      BASE_URL + "/auth/verify-reset-password";
  static final String RESET_PASSWORD_URL = BASE_URL + "/auth/reset-password";

  ///  <--- Profile --->
  static final String UPDATE_USER_PROFILE =
      BASE_URL + "/profile/update-profile";
  static final String UPLOAD_PROOFS_URL = BASE_URL + "/profile/upload-proofs";

  ///  <--- Seeker  --->
  static final String SEEKER_NEARBY_SERVICES_URL =
      BASE_URL + "/seeker/nearby-services";
  static final String POST_JOB_URL = BASE_URL + "/seeker/post-job";

  static final String UPDATE_JOB_URL = BASE_URL + "/seeker/update-job";
  static final String INVITE_PROVIDERS_TO_JOB_URL =
      BASE_URL + "/seeker/invite-providers";
  static final String GET_ALL_POSTED_JOBS_URL = BASE_URL + "/seeker/my-jobs";
  static final String GET_SERVICE_DETAILS_URL =
      BASE_URL + "/seeker/service-details";
  static final String SEARCH_SUGGESTIONS_URL =
      BASE_URL + "/seeker/search-suggestions";
  static String GET_ICAL_EVENTS = BASE_URL + "/seeker/ical/url-events";
  static String GET_ICAL_TEMPLATES_URL = BASE_URL + "/seeker/ical/templates";
  static String CREATE_ICAL_TEMPLATE_URL = BASE_URL + "/seeker/ical/create-template";
  static String GET_TEMPLATE_EVENTS_URL = BASE_URL + "/seeker/ical/template-events";

  /// <--- Provider --->
  static final String AVAILABLE_JOBS_URL =
      BASE_URL + "/provider/available-jobs";
  static final String MY_SERVICES_URL = BASE_URL + "/provider/my-services";
  static final String ADD_SERVICE_URL = BASE_URL + "/provider/create-service";

  static final String APPLY_ON_JOB_URL = BASE_URL + "/provider/apply-on-job";
  static final String APPLIED_JOB_POSTS_URL =
      BASE_URL + "/provider/applied-job-posts";
  static final String INVITED_JOB_POSTS_URL =
      BASE_URL + "/provider/invited-job-posts";
  static final String ICAL_TEST_URL =
      "https://calendar.google.com/calendar/ical/en.usa%23holiday%40group.v.calendar.google.com/public/basic.ics";

  /// <--- Base Url  --->
  static String get BASE_URL =>
      kDebugMode
          ? (Platform.isAndroid
              ? "http://10.0.2.2:8000"
              : "http://127.0.0.1:8000")
          : "https://isquares-py.vercel.app";
  static String DELETE_JOB_URL(int id) => BASE_URL + "/seeker/delete-job/$id";

  static String GET_USER_PROFILE_URL(String id) => BASE_URL + "/profile/$id";


  /// <--- Chats --->
  static String GET_CHATROOMS = BASE_URL + "/chats/chatrooms";
  static String OPEN_CHATROOM = BASE_URL + "/chats/open-chatroom";
  static String SEND_MESSAGE = BASE_URL + "/chats/send-message";
}
