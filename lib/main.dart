import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:i_squares/controllers/seeker/user_profile_controller.dart';
import 'package:i_squares/utils/constants.dart';
import 'package:i_squares/utils/screen_chooser.dart';
import 'package:i_squares/utils/theme.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';

void main() async {
  var widgetsBinding = WidgetsFlutterBinding.ensureInitialized();
  FlutterNativeSplash.preserve(widgetsBinding: widgetsBinding);
  await GetStorage.init();
  var screen = await ScreenChooser.home;
  MapboxOptions.setAccessToken(
      mapBoxAccessToken);

  FlutterNativeSplash.remove();
  runApp(
    MyApp(
      screen: screen,
    ),
  );
}

class MyApp extends StatelessWidget {
  final Widget screen;

  MyApp({
    required this.screen,
  });

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      debugShowCheckedModeBanner: false,
      home: screen,
      theme: ThemeServices.themeData,
      initialBinding: BindingsBuilder(() {
        Get.put(UserProfileController());
      }),
      builder: (context, widget) {
        return AnnotatedRegion<SystemUiOverlayStyle>(
          value: SystemUiOverlayStyle(
            statusBarColor: Theme.of(context).appBarTheme.backgroundColor,
            statusBarIconBrightness: Brightness.dark,
            systemNavigationBarColor:
                Theme.of(context).buttonTheme.colorScheme!.surface,
            systemNavigationBarIconBrightness: Brightness.dark,
            statusBarBrightness: Brightness.dark,
            systemStatusBarContrastEnforced: true,
            systemNavigationBarDividerColor: Colors.transparent,
            systemNavigationBarContrastEnforced: true,
          ),
          child: widget!,
        );
      },
    );
  }
}

// Future<void> addIconToStyle(MapboxMap map, String iconName, String assetPath) async {
//   // Load the image as byte data
//   final ByteData imageData = await rootBundle.load(assetPath);
//   final Uint8List bytes = imageData.buffer.asUint8List();
//
//   // Add the image to the map's style
//   map.styleManager.addStyleImage(
//     iconName,
//     bytes,
//     imageFormat: ImageFormat.PNG, // Specify the format
//   );
// }
