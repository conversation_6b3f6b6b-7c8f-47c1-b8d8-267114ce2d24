import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:i_squares/controllers/common/orders_controller.dart';
import 'package:i_squares/widgets/not_found.dart';
import 'package:i_squares/widgets/paginated_list_view.dart';

import '../items/item_active.dart';
import '../screens/screen_active_service_details.dart';

class LayoutProviderOrders extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final controller = Get.put(OrdersController());
    var orderStatusLabels = {
      "all": "All",
      "not_started": "Pending",
      "active": "Active",
      "closed": "Completed",
      "cancelled": "Cancelled",
    };

    return Column(
      children: [
        // Segmented Control for filtering
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: Obx(
            () => CupertinoSlidingSegmentedControl<String>(
              children: Map<String, Widget>.fromEntries(
                orderStatusLabels.entries.map(
                  (e) => MapEntry(e.key, Text(e.value)),
                ),
              ),
              onValueChanged: (value) {
                controller.selectedOrderStatus.value = value.toString();
                controller.refreshOrders();
              },
              proportionalWidth: true,
              groupValue: controller.selectedOrderStatus.value,
              padding: const EdgeInsets.all(4),
            ),
          ),
        ),

        // Orders List
        Expanded(
          child: Obx(
            () => PaginatedListView(
              physics: BouncingScrollPhysics(),
              scrollController: ScrollController(),
              tag: "provider_orders_${controller.selectedOrderStatus.value}",
              future:
                  (offset, limit) => controller.fetchOrders(
                    offset: offset,
                    limit: limit,
                    status:
                        controller.selectedOrderStatus.value == 'all'
                            ? null
                            : controller.selectedOrderStatus.value,
                  ),
              emptyWidgetOrText: NotFound(
                context,
                "No${controller.selectedOrderStatus.value == 'all' ? '' : " " + orderStatusLabels[controller.selectedOrderStatus.value].toString()} orders found",
              ),
              itemBuilder: (BuildContext context, int index, item) {
                return GestureDetector(
                  onTap: () {
                    Get.to(() => ScreenActiveServiceDetails());
                  },
                  child: ItemActive(),
                );
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSegment(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      child: Text(
        title,
        style: TextStyle(fontSize: 13, fontWeight: FontWeight.w500),
      ),
    );
  }
}
