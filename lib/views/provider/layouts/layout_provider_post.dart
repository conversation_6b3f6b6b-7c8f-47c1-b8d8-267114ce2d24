import 'package:flutter/material.dart';
import 'package:i_squares/widgets/my_tab.dart';

import 'layout_applied.dart';
import 'layout_invited_post.dart';
import 'layout_provider_orders.dart';

class LayoutProviderPost extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 3,
      child: Scaffold(
        appBar: AppBar(
          title: Text("Overview"),
          bottom: TabBar(
            tabs: [
              MyTab(text: "Invited jobs"),
              MyTab(text: "Applied"),
              MyTab(text: "Orders"),
            ],
          ),
        ),
        body: TabBarView(
          children: [
            LayoutInvitedPost(),
            LayoutApplied(),
            LayoutProviderOrders(),
          ],
        ),
      ),
    );
  }
}
