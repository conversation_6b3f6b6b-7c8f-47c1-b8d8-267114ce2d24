import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:i_squares/controllers/common/inbox_controller.dart';
import 'package:i_squares/widgets/paginated_list_view.dart';

import '../../seeker/items/item_chat.dart';
import '../screens/screen_provider_chat.dart';

class LayoutProviderInbox extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    var controller = Get.put(InboxController());

    return PaginatedListView(
      shrinkWrap: true,
      physics: AlwaysScrollableScrollPhysics(),
      future: controller.fetchChatrooms,
      scrollController: ScrollController(),
      tag: "user_chatrooms",
      emptyWidgetOrText: "No chats found",
      itemBuilder: (context, index, item) {
        return GestureDetector(
          onTap: () async {
            await Get.to(
              () => ScreenProviderChat(selectedUser: item['user']),
            );
            Get.find<PaginationController<Map<String, dynamic>>>(tag: "user_chatrooms").restart();
          },
          child: ItemChat(item),
        );
      },
    ).marginSymmetric(vertical: 4, horizontal: 8);
  }
}
