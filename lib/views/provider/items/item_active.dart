import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../utils/constants.dart';
import '../../../widgets/medium_text.dart';
import '../../../widgets/small_text.dart';

class ItemActive extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 10, vertical: 6),
      padding: EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(17),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: .1),
              blurRadius: 2,
            )
          ]),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ListTile(
            title: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                SmallText(
                  text: "Figma",
                  fontFamily: "Poppins",
                  fontWeight: FontWeight.w500,
                  color: Colors.black,
                ),
                MediumText(
                  text: "UIUX Designer",
                  color: Colors.black,
                  fontFamily: "Poppins",
                  fontWeight: FontWeight.w700,
                  size: 16,
                ),
                SmallText(
                  text: "Jakarta, Indonesia",
                  size: 13,
                ),
              ],
            ),
            trailing: Column(
              children: [
                SmallText(
                  text: "\$15/h-\$20/h",
                  color: Colors.black.withValues(alpha: .5),
                  size: 12,
                ),
                SmallText(
                  size: 12,
                  text: "Price",
                  color: Colors.black,
                  fontWeight: FontWeight.w600,
                ),
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
            decoration: BoxDecoration(
              color: gryClr,
              borderRadius: BorderRadius.circular(8),
            ),
            child: SmallText(
              text: "25 Dec 2023 - 25 Dec 2024",
              fontFamily: "Plus",
              fontWeight: FontWeight.w500,
              color: Colors.black,
              size: 14,
            ),
          ),
          SmallText(
            text:
                "Designing digital magic with a pixel-perfect touch.speaks simplicity in every click.",
            size: 12,
            fontFamily: "Poppins",
          ).marginSymmetric(vertical: 5),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 10, vertical: 3),
            decoration: BoxDecoration(
              color: appColor.withValues(alpha: .1),
              borderRadius: BorderRadius.circular(3),
            ),
            child: SmallText(
              text: "In-Progress",
              color: appColor,
              size: 12,
            ),
          )
        ],
      ),
    );
  }
}
