import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:i_squares/controllers/common/auth_controller.dart';
import 'package:i_squares/utils/constants.dart';
import 'package:i_squares/views/seeker/screens/screen_home.dart';

import '../../../utils/show_message.dart';
import '../../../widgets/medium_text.dart';
import '../../../widgets/small_text.dart';

class ScreenVerifyIdentity extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    AuthController controller = Get.put(AuthController());
    var loading = false.obs;

    return Scaffold(
      appBar: AppBar(
        toolbarHeight: 80,
        automaticallyImplyLeading: false,
        title: SvgPicture.asset(
          "assets/images/png/app_logo_2.svg",
          height: 60,
          width: 60,
        ).marginOnly(top: 20),
      ),
      body: Container(
        margin: EdgeInsets.symmetric(horizontal: 15, vertical: 40),
        padding: EdgeInsets.symmetric(horizontal: 20, vertical: 20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(14),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: .25),
              blurRadius: 10,
            ),
          ],
        ),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              MediumText(
                text: "Verify your identity",
                fontWeight: FontWeight.w500,
                size: 18,
                color: Color(0xFF1F1F1F),
              ).marginSymmetric(vertical: 5),
              SmallText(
                text:
                    "Please upload your Identity card below for completing your verification.",
                fontWeight: FontWeight.w500,
                size: 13,
                color: Color(0xFF1F1F1F).withValues(alpha: .65),
              ),
              GestureDetector(
                onTap: () {
                  controller.pickImage();
                },
                child: Obx(() {
                  return controller.image.value == null
                      ? Container(
                          width: Get.width * .8,
                          margin:
                              EdgeInsets.symmetric(horizontal: 15, vertical: 30),
                          padding:
                              EdgeInsets.symmetric(horizontal: 12, vertical: 20),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            border: Border.all(
                                color: Colors.black.withValues(alpha: .11)),
                            borderRadius: BorderRadius.circular(15),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: .1),
                                blurRadius: 3,
                                spreadRadius: 1,
                              ),
                            ],
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              SmallText(
                                text: "Upload your Identity card",
                                color: Color(0xFF7C7C7C),
                                size: 12,
                                fontWeight: FontWeight.w400,
                                fontFamily: "Poppins",
                              ),
                              Container(
                                height: 30,
                                width: 100,
                                alignment: Alignment.center,
                                margin: EdgeInsets.symmetric(vertical: 10),
                                padding: EdgeInsets.symmetric(
                                    horizontal: 6, vertical: 4),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  border: Border.all(color: Colors.black),
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    SmallText(
                                      text: "Upload",
                                      color: Colors.black,
                                      fontWeight: FontWeight.w600,
                                    ),
                                    Icon(
                                      Icons.add,
                                      size: 18,
                                      color: Colors.black,
                                    )
                                  ],
                                ),
                              )
                            ],
                          ),
                        )
                      : Align(
                          alignment: Alignment.center,
                          child: Padding(
                            padding: EdgeInsets.symmetric(
                                horizontal: 15, vertical: 30),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(15),
                              child: Image.file(
                                File(controller.image.value!.path),
                                fit: BoxFit.cover,
                                height: 110,
                                width: 240,
                              ),
                            ),
                          ),
                        );
                }),
              ),
          
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Obx(() {
                    return Transform.translate(
                      offset: Offset(0, -12),
                      child: Checkbox(
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(3),
                        ),
                        activeColor: appColor,
                        value: controller.isChecked.value,
                        onChanged: (newValue) {
                          controller.isChecked.value = newValue!;
                        },
                      ),
                    );
                  }),
                  Expanded(
                    child: Text(
                      "I hereby confirm that I have uploaded a clear and legible photo of my identity card, and I ensure that all details on my card are visible and accurate.",
                      overflow: TextOverflow.visible,
                      maxLines: 6,
                      textAlign: TextAlign.justify,
                      style: TextStyle(
                        fontFamily: "Satoshi",
                        fontWeight: FontWeight.w600,
                        fontSize: 13,
                        color: Colors.black,
                      ),
                    ),
                  )
                ],
              ),
              // SizedBox(height: 30),
              Obx(() {
                return ElevatedButton(
                  onPressed: (loading.value ||
                          controller.isChecked.value == false)
                      ? null
                      : () async {
                          loading.value = true;
                          var response = await controller
                              .uploadProofs([File(controller.image.value!.path)]);
                          loading.value = false;
          
                          if (response == "success") {
                            Get.offAll(() => ScreenHome());
                          } else {
                            showMessage(response);
                          }
                        },
                  child: Text(
                    loading.value ? "Please wait..." : "Submit",
                    style: TextStyle(
                      fontSize: 16,
                      decoration: TextDecoration.none,
                      fontFamily: "Satoshi",
                      fontWeight: FontWeight.w700,
                      color: Colors.white,
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: appColor,
                    fixedSize: Size(Get.width, 44),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30),
                    ),
                  ),
                );
              }).marginOnly(top: 15, bottom: 15, left: 10, right: 10),
              Text(
                "If you are facing any difficulties, please get in touch with us on Email",
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontFamily: "Poppins",
                  fontSize: 10,
                  color: Color(0xff1F1F1F).withValues(alpha: .65),
                ),
              ).marginSymmetric(horizontal: 15),
            ],
          ),
        ),
      ),
    );
  }
}
