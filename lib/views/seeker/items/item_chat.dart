import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:i_squares/extensions/date_time_ext.dart';

import '../../../widgets/small_text.dart';

class ItemChat extends StatelessWidget {
  final Map<String, dynamic> data;

  ItemChat(this.data);

  @override
  Widget build(BuildContext context) {
    var user = data['user'];

    return ListTile(
      isThreeLine: true,
      leading: ClipRRect(
        borderRadius: BorderRadius.circular(30),
        child: CachedNetworkImage(
          height: 40,
          width: 40,
          fit: BoxFit.cover,
          placeholder:
              (context, url) => Image.asset("assets/images/png/holder.jpg"),
          imageUrl: user?['image'].toString()??"",
        ),
      ),
      title: SmallText(
        text: user?['name'].toString()??"Unknown",
        color: Color(0xFF242C43),
        fontFamily: "Poppins",
        fontWeight: FontWeight.w600,
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SmallText(
            text: data['last_message']?['text'].toString()??"",
            maxLines: 1,
            size: 14,
            color: Color(0xFF242C43).withValues(alpha: .7),
            fontFamily: "Plus",
            overflow: TextOverflow.ellipsis,
            fontWeight: FontWeight.w300,
          ),
          Divider(color: Colors.black.withValues(alpha: .2)),
        ],
      ),
      trailing: SmallText(
        text: DateTime.parse(data['last_message']?['created_at'].toString()??"2025-06-06T14:30:00Z").toRelaventTime,
        color: Color(0xFF242C43).withValues(alpha: .5),
        size: 14,
      ),
    );
  }
}
