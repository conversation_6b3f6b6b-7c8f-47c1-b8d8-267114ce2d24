import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:i_squares/controllers/seeker/create_post_controller.dart';
import 'package:i_squares/extensions/future_ext.dart';
import 'package:i_squares/utils/constants.dart';
import 'package:i_squares/utils/show_message.dart';
import 'package:i_squares/views/seeker/screens/screen_add_location.dart';
import 'package:i_squares/views/seeker/screens/screen_invite.dart';
import 'package:intl/intl.dart';

import '../../../widgets/back_btn.dart';
import '../../../widgets/border_field.dart';
import '../../../widgets/my_elevated_btn.dart';
import '../../../widgets/small_text.dart';
import '../items/item_requirements.dart';

class ScreenCreateOrUpdatePost extends StatelessWidget {
  final cPFormKey = GlobalKey<FormState>();
  final Map<String, dynamic>? job;

  final bool isTemplate;
  ScreenCreateOrUpdatePost({this.job, this.isTemplate = false});

  @override
  Widget build(BuildContext context) {
    var controller = Get.put(CreatePostController());

    TextEditingController titleController = TextEditingController(
      text: job == null ? "" : job!['title'],
    );
    TextEditingController hourlyRController = TextEditingController(
      text: job == null ? "" : job!['post_price'].toInt().toString(),
    );
    TextEditingController descriptionController = TextEditingController(
      text: job == null ? "" : job!['description'],
    );
    RxDouble priorityLevel = RxDouble(double.tryParse((job?['priority']).toString()) ?? 0.0);
    var loading = false.obs;

    /// date picker
    final Rx<DateTime?> selectStartDate = Rx(
      job?['start_date'] != null ? DateTime.parse(job!['start_date']) : null,
    );
    final Rx<DateTime?> selectEndDate = Rx(
      job?['end_date'] != null ? DateTime.parse(job!['end_date']) : null,
    );
    final nowDate = DateTime.now();
    RxList<TextEditingController> requirementList = RxList(
      (job?['requirements'] as List? ?? [])
          .map((e) => TextEditingController(text: e))
          .toList(),
    );
    var selectedCategory = Rx<int?>(job?['category_id']);
    Rx<LatLng?> selectedLocation = Rx<LatLng?>(null);
    var selectedAddress = Rx<String?>(null);

    if (job?['latitude'] != null && job?['longitude'] != null) {
      getLocationName(job!['latitude'], job!['longitude']).then((address) {
        selectedAddress.value = address;
      });
      selectedLocation.value = LatLng(job!['latitude'], job!['longitude']);
    }

    return Scaffold(
      appBar: AppBar(
        leading: BackBtn(color: Colors.white),
        title: Text(
          "${job == null
              ? isTemplate
                  ? "New"
                  : "Create"
              : "Update"} Job ${isTemplate ? "Template" : "Post"}",
        ),
      ),
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 8.0, horizontal: 10),
          child: SingleChildScrollView(
            physics: BouncingScrollPhysics(),
            child: Form(
              key: cPFormKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SmallText(
                    text: "Job title",
                    color: Color(0xFF999999),
                    fontWeight: FontWeight.w500,
                  ).marginSymmetric(vertical: 4),
                  Obx(() {
                    return BorderField(
                      hint: "Figma",
                      readOnly: loading.value,
                      controller: titleController,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'service title is required';
                        }
                        return null;
                      },
                    );
                  }).marginSymmetric(vertical: 4),
                  SmallText(
                    text: "Set Hourly rate",
                    color: Color(0xFF999999),
                    fontWeight: FontWeight.w500,
                  ).marginSymmetric(vertical: 4),
                  BorderField(
                    hint: "30",
                    prefix: Text("\$"),
                    suffix: Text("/ hr"),
                    readOnly: loading.value,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Hourly rate is required';
                      } else if ((double.tryParse(value) ?? 0) < 5) {
                        return "Minimum hourly rate is \$5";
                      }
                      return null;
                    },
                    controller: hourlyRController,
                    keyboardType: TextInputType.number,
                  ).marginSymmetric(vertical: 4),
                  SmallText(
                    text: "Description",
                    color: Color(0xFF999999),
                    fontWeight: FontWeight.w500,
                  ).marginSymmetric(vertical: 4),
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 10),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(7),
                      border: Border.all(color: Color(0xffD4D4D4)),
                    ),
                    child: TextFormField(
                      readOnly: loading.value,
                      maxLines: 3,
                      keyboardType: TextInputType.text,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'description is required';
                        }
                        return null;
                      },
                      controller: descriptionController,
                      decoration: InputDecoration(
                        border: InputBorder.none,
                        hintText:
                            "Amet minim mollit non deserunt ullamco "
                            "est sit aliqua dolor do amet sint. Velit "
                            "officia consequat duis enim velit mollit.",
                      ),
                    ),
                  ).marginSymmetric(vertical: 4),
                  SmallText(
                    text: "Select category",
                    color: Color(0xFF999999),
                    fontWeight: FontWeight.w500,
                  ).marginSymmetric(vertical: 4),
                  Container(
                    width: Get.width,
                    decoration: BoxDecoration(
                      border: Border.all(color: Color(0xffd4d4d4)),
                      borderRadius: BorderRadius.circular(7),
                    ),
                    child: Obx(() {
                      return DropdownButton<int>(
                        borderRadius: BorderRadius.circular(12),
                        underline: SizedBox(),
                        hint: Text(
                          "Select a category",
                          style: TextStyle(fontFamily: "Satoshi"),
                        ),
                        isExpanded: true,
                        value: selectedCategory.value,
                        items:
                            controller.categories.map((e) {
                              return DropdownMenuItem(
                                value: e['id'] as int,
                                child: ListTile(
                                  title: Text(e['name'].toString()),
                                  trailing: Text('${e['services']} services'),
                                ),
                              );
                            }).toList(),
                        onChanged: (int? newValue) {
                          selectedCategory.value = newValue;
                        },
                      );
                    }).marginSymmetric(horizontal: 5),
                  ).marginSymmetric(vertical: 4),
                  SizedBox(height: 10),
                  if (!isTemplate) ...[
                    Row(
                      children: [
                        Expanded(
                          child: SmallText(
                            text: "Start Date",
                            color: Color(0xFF999999),
                            fontWeight: FontWeight.w500,
                          ).marginSymmetric(vertical: 4),
                        ),
                        SizedBox(width: 20),
                        Expanded(
                          child: SmallText(
                            text: "End Date",
                            color: Color(0xFF999999),
                            fontWeight: FontWeight.w500,
                          ).marginSymmetric(vertical: 4),
                        ),
                      ],
                    ),
                    Row(
                      children: [
                        Expanded(
                          child: TextButton(
                            onPressed: () async {
                              var selectedDate = await showDatePicker(
                                context: context,
                                initialDate: nowDate,
                                firstDate: nowDate,
                                lastDate:
                                    selectEndDate.value ??
                                    DateTime(
                                      nowDate.year,
                                      nowDate.month + 1,
                                      nowDate.day,
                                    ),
                              );

                              if (selectedDate != null) {
                                selectStartDate.value = selectedDate;
                              }
                            },
                            child: Container(
                              alignment: Alignment.centerLeft,
                              padding: EdgeInsets.symmetric(horizontal: 10),
                              height: 50,
                              decoration: BoxDecoration(
                                color: Colors.white,
                                border: Border.all(color: Color(0xffd4d4d4)),
                                borderRadius: BorderRadius.circular(6),
                              ),
                              child: Obx(() {
                                return SmallText(
                                  text: DateFormat("dd MMM yyyy").format(
                                    selectStartDate.value ?? DateTime.now(),
                                  ),
                                  color: Colors.black.withValues(alpha: .5),
                                );
                              }),
                            ),
                          ).marginSymmetric(vertical: 4),
                        ),
                        SmallText(
                          text: "-",
                          color: Colors.black,
                        ).marginSymmetric(horizontal: 5),
                        Expanded(
                          child: TextButton(
                            onPressed: () async {
                              var selectedDate = await showDatePicker(
                                context: context,
                                initialDate: selectStartDate.value ?? nowDate,
                                firstDate: selectStartDate.value ?? nowDate,
                                lastDate: DateTime(
                                  nowDate.year,
                                  nowDate.month + 1,
                                  nowDate.day + 2,
                                ),
                              );

                              if (selectedDate != null) {
                                selectEndDate.value = selectedDate;
                              }
                            },
                            child: Container(
                              alignment: Alignment.centerLeft,
                              padding: EdgeInsets.symmetric(horizontal: 10),
                              height: 50,
                              decoration: BoxDecoration(
                                color: Colors.white,
                                border: Border.all(color: Color(0xffd4d4d4)),
                                borderRadius: BorderRadius.circular(6),
                              ),
                              child: Obx(() {
                                return SmallText(
                                  text: DateFormat("dd MMM yyyy").format(
                                    selectEndDate.value ??
                                        DateTime(
                                          nowDate.year,
                                          nowDate.month + 1,
                                          nowDate.day,
                                        ),
                                  ),
                                  color: Colors.black.withValues(alpha: .5),
                                );
                              }),
                            ),
                          ).marginSymmetric(vertical: 4),
                        ),
                      ],
                    ),
                    SizedBox(height: 10),
                  ],
                  SmallText(
                    text: "Requirements",
                    color: Color(0xFF999999),
                    fontWeight: FontWeight.w500,
                  ).marginSymmetric(vertical: 4),
                  Obx(() {
                    return Column(
                      children:
                          requirementList
                              .map(
                                (e) => ItemRequirements(
                                  requireController: e,
                                  onRemove: () {
                                    requirementList.remove(e);
                                  },
                                ),
                              )
                              .toList(),
                    );
                  }),
                  TextButton(
                    onPressed: () {
                      requirementList.add(TextEditingController());
                    },
                    child: SmallText(
                      text: "Add new",
                      color: Colors.black,
                      fontWeight: FontWeight.w600,
                    ).marginSymmetric(vertical: 4),
                  ),
                  GestureDetector(
                    onTap: () async {
                      var data = await Get.to(
                        () => ScreenAddLocation(
                          initialLocation: selectedLocation.value,
                        ),
                      );
                      if (data != null) {
                        selectedLocation.value = data['location'];
                        selectedAddress.value = data['address'];
                      }
                    },
                    child: DottedBorder(
                      borderType: BorderType.RRect,
                      dashPattern: [2, 2],
                      radius: Radius.circular(17),
                      padding: EdgeInsets.symmetric(vertical: 20),
                      color: Color(0xffBABABA),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Expanded(
                            child: Obx(() {
                              return SmallText(
                                text: selectedAddress.value ?? "Add location",
                                maxLines: 2,
                              );
                            }).marginOnly(right: 8),
                          ),
                          SvgPicture.asset("assets/images/svg/loc2.svg"),
                        ],
                      ).marginSymmetric(horizontal: 10),
                    ).marginSymmetric(vertical: 10),
                  ),
                  SmallText(
                    text: "Priority level",
                    color: Color(0xFF999999),
                    fontWeight: FontWeight.w500,
                  ).marginSymmetric(vertical: 4),
                  Obx(() {
                    return Slider(
                      divisions: 4,
                      label: "${priorityLevel.value.toInt()}",
                      activeColor: appColor,
                      min: 1.0,
                      max: 5.0,
                      thumbColor: Colors.white,
                      value: priorityLevel.value,
                      onChanged: (newValue) {
                        priorityLevel.value = newValue;
                      },
                    );
                  }),
                  Obx(() {
                    return MyElevatedBtn(
                      text: job != null ? "Update" : "Continue",
                      loading: loading.value,
                      onTap: () async {
                        if (!cPFormKey.currentState!.validate()) {
                          showMessage("Please fix the errors above");
                          return;
                        } else if (selectedCategory.value == null) {
                          showMessage("Please select category");
                          return;
                        } else if (requirementList.isEmpty) {
                          showMessage("Please add at least 1 requirement");
                          return;
                        } else if (selectedLocation.value == null) {
                          showMessage("Please select location");
                          return;
                        }

                        if (isTemplate) {
                          Get.back(
                            result: {
                              "title": titleController.text,
                              "description": descriptionController.text,
                              "post_price":
                                  double.tryParse(hourlyRController.text) ?? 0,
                              "category_id": selectedCategory.value!,
                              "latitude": selectedLocation.value!.latitude,
                              "longitude": selectedLocation.value!.longitude,
                              "priority": priorityLevel.value.toInt(),
                              "requirements":
                                  requirementList
                                      .map((e) => e.text)
                                      .where((e) => e.trim().isNotEmpty)
                                      .toList()
                            },
                          );
                          return;
                        }

                        if (selectStartDate.value == null ||
                            selectEndDate.value == null) {
                          showMessage("Please select start and end dates");
                          return;
                        }

                        showAlertBottomSheet(
                          context,
                          title: "Confirm ${job != null ? "Update" : "Post"}",
                          message:
                              "You are about to ${job != null ? "update your" : "create a new"} job post, are you sure?",
                          confirmButtonText: 'Confirm',
                          dismissButtonText: "Cancel",
                          onConfirmButtonPressed: () async {
                            var data = {
                              "job": {
                                "title": titleController.text,
                                "description": descriptionController.text,
                                "post_price":
                                    double.tryParse(hourlyRController.text) ??
                                    0,
                                "category_id": selectedCategory.value!,
                                "latitude": selectedLocation.value!.latitude,
                                "longitude": selectedLocation.value!.longitude,
                                "priority": priorityLevel.value.toInt(),
                                // saving date as timestamp with zone string
                                "start_date":
                                    selectStartDate.value!.toIso8601String(),
                                "end_date":
                                    selectEndDate.value!.toIso8601String(),
                              },
                              "requirements":
                                  requirementList
                                      .map((e) => e.text)
                                      .where((e) => e.trim().isNotEmpty)
                                      .toList(),
                            };

                            var response = await (job != null
                                    ? controller.updateJobPost(data, job!['id'])
                                    : controller.createJobPost(data))
                                .link(loading);

                            var status = response.key;

                            if (status == 'success') {
                              if (job != null) {
                                showMessage("Job updated successfully");
                                Navigator.pop(context);
                                return;
                              }
                              var jobCreated = response.value;
                              Get.off(
                                () => ScreenInvite(jobId: jobCreated['id']),
                              );
                            } else {
                              showMessage(status);
                            }
                          },
                          onDismissButtonPressed: () {},
                        );

                        // if (cPFormKey.currentState!.validate()) {
                        //
                        //   // Get.to(
                        //   //       () =>
                        //   //       ScreenInvite(
                        //   //         title: controller.titleController.text,
                        //   //         endDate: controller.selectEndDate.value,
                        //   //         startDate: controller.selectStartDate.value,
                        //   //         category: controller.selectedCategory.value,
                        //   //         description: controller.descripController.text,
                        //   //         hourlyRate: controller.hourlyRController.text,
                        //   //         requirements: controller.addItem.map((e) => e.text).toList(),
                        //   //       ),
                        //   // );
                        // }
                      },
                    );
                  }),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
