import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:i_squares/utils/map_utils.dart';
import 'package:i_squares/views/seeker/layouts/layout_chat.dart';
import 'package:i_squares/views/seeker/layouts/layout_home.dart';
import 'package:i_squares/views/seeker/layouts/layout_posts.dart';
import 'package:i_squares/views/seeker/layouts/layout_profile.dart';
import 'package:i_squares/views/seeker/screens/screen_create_post.dart';
import 'package:i_squares/widgets/paginated_list_view.dart';

class ScreenHome extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    var currentIndex = 0.obs;


    return PopScope(
      canPop: false,
      child: Scaffold(
        bottomNavigationBar: BottomAppBar(
          elevation: 30,
          shadowColor: Colors.grey,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Obx(() {
                return Row(
                  children: [
                    GestureDetector(
                      onTap: () {
                        currentIndex.value = 0;
                      },
                      child: Container(
                        alignment: Alignment.center,
                        height: 35,
                        width: 35,
                        decoration: BoxDecoration(
                          color:
                              currentIndex.value == 0
                                  ? Color(0xff49454F).withValues(alpha: .08)
                                  : Colors.transparent,
                          shape: BoxShape.circle,
                        ),
                        child: SvgPicture.asset(
                          "assets/images/svg/home.svg",
                          colorFilter: ColorFilter.mode(
                            currentIndex.value == 0
                                ? Colors.black
                                : Colors.black.withValues(alpha: .5),
                            BlendMode.srcIn,
                          ),
                        ),
                      ).marginOnly(right: 12),
                    ),
                    GestureDetector(
                      onTap: () {
                        currentIndex.value = 1;
                      },
                      child: Container(
                        alignment: Alignment.center,
                        height: 35,
                        width: 35,
                        decoration: BoxDecoration(
                          color:
                              currentIndex.value == 1
                                  ? Color(0xff49454F).withValues(alpha: .08)
                                  : Colors.transparent,
                          shape: BoxShape.circle,
                        ),
                        child: SvgPicture.asset(
                          "assets/images/svg/bag.svg",
                          colorFilter: ColorFilter.mode(
                            currentIndex.value == 1
                                ? Colors.black
                                : Colors.black.withValues(alpha: .5),
                            BlendMode.srcIn,
                          ),
                        ),
                      ).marginOnly(right: 12),
                    ),
                    GestureDetector(
                      onTap: () {
                        currentIndex.value = 2;
                      },
                      child: Container(
                        alignment: Alignment.center,
                        height: 35,
                        width: 35,
                        decoration: BoxDecoration(
                          color:
                              currentIndex.value == 2
                                  ? Color(0xff49454F).withValues(alpha: .08)
                                  : Colors.transparent,
                          shape: BoxShape.circle,
                        ),
                        child: SvgPicture.asset(
                          "assets/images/svg/msg.svg",
                          colorFilter: ColorFilter.mode(
                            currentIndex.value == 2
                                ? Colors.black
                                : Colors.black.withValues(alpha: .5),
                            BlendMode.srcIn,
                          ),
                        ),
                      ).marginOnly(right: 12),
                    ),
                    GestureDetector(
                      onTap: () {
                        currentIndex.value = 3;
                      },
                      child: Container(
                        alignment: Alignment.center,
                        height: 35,
                        width: 35,
                        decoration: BoxDecoration(
                          color:
                              currentIndex.value == 3
                                  ? Color(0xff49454F).withValues(alpha: .08)
                                  : Colors.transparent,
                          shape: BoxShape.circle,
                        ),
                        child: SvgPicture.asset(
                          "assets/images/svg/pro.svg",
                          colorFilter: ColorFilter.mode(
                            currentIndex.value == 3
                                ? Colors.black
                                : Colors.black.withValues(alpha: .5),
                            BlendMode.srcIn,
                          ),
                        ),
                      ).marginOnly(right: 12),
                    ),
                  ],
                );
              }),
              FloatingActionButton(
                highlightElevation: 0,
                splashColor: Colors.transparent,
                onPressed: () async {
                  await Get.to(() => ScreenCreateOrUpdatePost());
                  await PaginationController.restartAll();
                },
                child: Icon(Icons.add, color: Colors.black),
              ),
            ],
          ),
        ),
        body: Obx(() {
          return SafeArea(
            child:
                [
                  buildLayoutAfterLocation((lat, lng) => LayoutHome(lat, lng)),
                  LayoutPosts(),
                  LayoutChat(),
                  LayoutProfile(),
                ][currentIndex.value],
          );
        }),
      ),
    );
  }
}
