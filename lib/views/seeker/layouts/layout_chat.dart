import 'package:flutter/material.dart';
import 'package:i_squares/views/provider/layouts/layout_provider_inbox.dart';
import 'package:i_squares/views/seeker/layouts/layout_order_inbox.dart';

import '../../../widgets/my_tab.dart';

class LayoutChat extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        appBar: AppBar(
          title: Text("Messages Inbox"),
          bottom: TabBar(
            tabs: [MyTab(text: "Inbox"), MyTab(text: "Order Inbox")],
          ),
        ),
        body: TabBarView(children: [LayoutProviderInbox(), LayoutOrderInbox()]),
      ),
    );
  }
}
