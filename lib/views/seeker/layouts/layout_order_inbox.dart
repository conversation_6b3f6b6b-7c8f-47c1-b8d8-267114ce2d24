import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:i_squares/controllers/common/orders_controller.dart';
import 'package:i_squares/widgets/paginated_list_view.dart';

import '../items/item_order_inbox.dart';
import '../screens/screen_order_inbox_details.dart';

class LayoutOrderInbox extends StatelessWidget {
  @override
  Widget build(BuildContext context) {

    var controller = Get.put(OrdersController());

    return PaginatedListView(
      shrinkWrap: true,
      physics: AlwaysScrollableScrollPhysics(),
      future: (offset, limit) =>  controller.fetchOrders(offset: offset, limit: limit),
      scrollController: ScrollController(),
      tag: "user_orders",
      emptyWidgetOrText: "No active orders",
      itemBuilder: (context, index, item) {
        return GestureDetector(
          onTap: () async {
            await Get.to(
              () => ScreenOrderInboxDetails(),
            );
            Get.find<PaginationController<Map<String, dynamic>>>(tag: "user_chatrooms").restart();
          },
          child: ItemOrderInbox(),
        );
      },
    ).marginSymmetric(vertical: 4, horizontal: 8);
  }
}
