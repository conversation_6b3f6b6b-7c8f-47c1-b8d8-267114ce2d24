import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:i_squares/controllers/seeker/user_profile_controller.dart';
import 'package:i_squares/utils/constants.dart';
import 'package:i_squares/utils/screen_chooser.dart';
import 'package:i_squares/utils/show_message.dart';

import '../../common/screens/screen_login.dart';
import '../screens/screen_edit_profile.dart';
import '../screens/screen_settings.dart';

class LayoutProfile extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    var profileController = Get.put(UserProfileController())
      ..fetchCurrentUser();
    final ValueNotifier<bool> _reloadNotifier = ValueNotifier(false);
    var userModeSwitch = false.obs;

    return Scaffold(
      backgroundColor: Color(0xFFF8F9FA),
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        title: Text("Profile", style: TextStyle(fontWeight: FontWeight.w600)),
        automaticallyImplyLeading: false,
      ),
      body: ValueListenableBuilder(
        valueListenable: _reloadNotifier,
        builder: (_, v, w) {
          return RefreshIndicator(
            onRefresh: () async {
              await ScreenChooser.refreshToken();
              _reloadNotifier.value = !_reloadNotifier.value;
            },
            child: SingleChildScrollView(
              physics: AlwaysScrollableScrollPhysics(),
              child: Column(
                children: [
                  // Profile Header
                  Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                        bottomLeft: Radius.circular(20),
                        bottomRight: Radius.circular(20),
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          blurRadius: 10,
                          offset: Offset(0, 5),
                        ),
                      ],
                    ),
                    child: Obx(() {
                      var user = profileController.user.value;
                      return Column(
                        children: [
                          SizedBox(height: 20),
                          // Profile Image
                          Container(
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: appColor.withOpacity(0.2),
                                width: 4,
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.1),
                                  blurRadius: 8,
                                  spreadRadius: 2,
                                ),
                              ],
                            ),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(100),
                              child: CachedNetworkImage(
                                placeholder: (context, url) {
                                  return Image.asset(
                                    "assets/images/png/holder.jpg",
                                  );
                                },
                                height: 100,
                                width: 100,
                                fit: BoxFit.cover,
                                imageUrl: user['image']?.toString() ?? '',
                                errorWidget:
                                    (context, url, error) => Image.asset(
                                      "assets/images/png/holder.jpg",
                                    ),
                              ),
                            ),
                          ),
                          SizedBox(height: 16),

                          // User Name
                          Text(
                            user['name']?.toString() ?? 'User',
                            style: TextStyle(
                              fontSize: 22,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                          ),
                          SizedBox(height: 4),

                          // User Email
                          Text(
                            user['email']?.toString() ?? '<EMAIL>',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.black54,
                            ),
                          ),
                          SizedBox(height: 8),

                          // User Bio
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 24),
                            child: Text(
                              user['bio']?.toString() ?? 'No bio added yet',
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.black54,
                                fontStyle:
                                    user['bio'] == null
                                        ? FontStyle.italic
                                        : FontStyle.normal,
                              ),
                            ),
                          ),
                          SizedBox(height: 12),

                          // Verification Badge
                          Container(
                            padding: EdgeInsets.symmetric(
                              vertical: 4,
                              horizontal: 13,
                            ),
                            decoration: BoxDecoration(
                              color: appColor,
                              borderRadius: BorderRadius.circular(20),
                              boxShadow: [
                                BoxShadow(
                                  color: appColor.withOpacity(0.3),
                                  blurRadius: 8,
                                  offset: Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.verified,
                                  color: Colors.white,
                                  size: 16,
                                ),
                                SizedBox(width: 4),
                                Text(
                                  "Verified Account",
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.w500,
                                    fontSize: 14,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(height: 20),

                          // Edit Profile Button
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 24),
                            child: ElevatedButton.icon(
                              onPressed: () async {
                                await Get.to(ScreenEditProfile());
                                profileController.fetchCurrentUser();
                              },
                              icon: Icon(Icons.edit_outlined, size: 18),
                              label: Text("Edit Profile"),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.black,
                                foregroundColor: Colors.white,
                                elevation: 0,
                                minimumSize: Size(double.infinity, 45),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                            ),
                          ),
                          SizedBox(height: 24),
                        ],
                      );
                    }),
                  ),
                  SizedBox(height: 16),

                  // Account Mode Switch
                  Container(
                    margin: EdgeInsets.symmetric(horizontal: 16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          blurRadius: 10,
                          offset: Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Obx(() {
                      return SwitchListTile(
                        title: Row(
                          children: [
                            Icon(
                              Icons.business_center_outlined,
                              color: appColor,
                              size: 22,
                            ),
                            SizedBox(width: 12),
                            Text(
                              "Service Provider Mode",
                              style: TextStyle(
                                fontWeight: FontWeight.w600,
                                fontSize: 16,
                                color: Colors.black87,
                              ),
                            ),
                          ],
                        ),
                        subtitle: Text(
                          "Switch to provider account to offer services",
                          style: TextStyle(fontSize: 12, color: Colors.black54),
                        ),
                        value: userModeSwitch.value,
                        onChanged: (newValue) async {
                          userModeSwitch.value = newValue;
                          var (response, data) = await profileController
                              .updateUserMode('provider');
                          if (response == 'success') {
                            var screen = await ScreenChooser.homeByData(data);
                            Get.offAll(() => screen);
                          } else {
                            userModeSwitch.value = !userModeSwitch.value;
                            showMessage(response);
                          }
                        },
                        activeColor: appColor,
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                      );
                    }),
                  ),
                  SizedBox(height: 16),

                  // Settings and Logout Section
                  Container(
                    margin: EdgeInsets.symmetric(horizontal: 16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          blurRadius: 10,
                          offset: Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        _buildProfileMenuItem(
                          icon: CupertinoIcons.settings,
                          title: "Settings",
                          subtitle: "App preferences, notifications, privacy",
                          iconColor: Colors.blue,
                          onTap: () {
                            Get.to(ScreenSettings());
                          },
                        ),
                        Divider(
                          height: 1,
                          thickness: 1,
                          indent: 16,
                          endIndent: 16,
                        ),
                        _buildProfileMenuItem(
                          icon: Icons.power_settings_new_rounded,
                          title: "Logout",
                          subtitle: "Sign out from your account",
                          iconColor: Colors.red,
                          onTap: () {
                            _showLogoutDialog(context);
                          },
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 24),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildProfileMenuItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color iconColor,
    required VoidCallback onTap,
  }) {
    return ListTile(
      onTap: onTap,
      contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      leading: Container(
        padding: EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: iconColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(icon, color: iconColor, size: 24),
      ),
      title: Text(
        title,
        style: TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 16,
          color: Colors.black87,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(fontSize: 12, color: Colors.black54),
      ),
      trailing: Icon(Icons.arrow_forward_ios, color: Colors.black45, size: 16),
    );
  }

  void _showLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Column(
            children: [
              Container(
                padding: EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.red.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(Icons.logout_rounded, color: Colors.red, size: 32),
              ),
              SizedBox(height: 16),
              Text(
                "Logout",
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
            ],
          ),
          content: Text(
            "Are you sure you want to logout from your account?",
            textAlign: TextAlign.center,
            style: TextStyle(color: Colors.black54, fontSize: 16),
          ),
          actions: [
            Row(
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: Text(
                      "Cancel",
                      style: TextStyle(
                        color: Colors.black54,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    style: TextButton.styleFrom(
                      padding: EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                        side: BorderSide(color: Colors.black12),
                      ),
                    ),
                  ),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () async {
                      await GetStorage().remove('auth');
                      Get.offAll(() => ScreenLogin());
                    },
                    child: Text(
                      "Logout",
                      style: TextStyle(fontWeight: FontWeight.w600),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(vertical: 12),
                      elevation: 0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
          actionsPadding: EdgeInsets.fromLTRB(16, 0, 16, 16),
        );
      },
    );
  }
}
