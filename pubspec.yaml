name: i_squares
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.7.0 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  get: ^4.7.2
  flutter_svg: ^2.0.17
  image_picker: ^1.1.2
  cached_network_image: ^3.4.1
  google_maps_flutter: ^2.12.1
  card_swiper: ^3.0.1
  flutter_rating_bar: ^4.0.1
  dotted_border: ^2.1.0
  intl: ^0.19.0
  percent_indicator: ^4.2.5
  flutter_native_splash: ^2.4.1
  syncfusion_flutter_sliders: ^26.2.13
  get_storage: ^2.1.1
  dio: ^5.8.0+1
  location: ^8.0.0
  geolocator: ^13.0.4
  flutter_markdown: ^0.7.7
  url_launcher: ^6.3.1
  geocoding: ^3.0.0
  file_picker: ^10.1.0
  extended_text: ^15.0.2
  flutter_cache_manager: ^3.4.1
  open_file: ^3.5.10
  mapbox_maps_flutter: ^2.7.0
  debounce_controller: ^1.0.1
  font_awesome_flutter: ^10.8.0
  pinput: ^5.0.1
  redis: ^4.0.0
  shorebird_redis_client: ^0.0.7
  dedis: ^0.4.1
  ably_flutter: ^1.2.37
  firebase_core: ^3.13.0
  firebase_messaging: ^15.2.5
  flutter_local_notifications: ^19.1.0
  sticky_grouped_list: ^3.1.0
  auto_size_text: ^3.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter

flutter_native_splash:
  color: "#FFFFFF"
  image: "assets/images/png/logo.png"
  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
#  flutter_lints: ^2.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/png/
    - assets/images/svg/
    - assets/md/
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Satoshi
      fonts:
        - asset: assets/fonts/Satoshi-Regular.otf
        - asset: assets/fonts/Satoshi-Bold.otf
        - asset: assets/fonts/Satoshi-Light.otf
        - asset: assets/fonts/Satoshi-Medium.otf
    - family: Poppins
      fonts:
        - asset: assets/fonts/Poppins-Regular.ttf
        - asset: assets/fonts/Poppins-Bold.ttf
        - asset: assets/fonts/Poppins-Light.ttf
        - asset: assets/fonts/Poppins-Medium.ttf
        - asset: assets/fonts/Poppins-Thin.ttf
    - family: Plus
      fonts:
        - asset: assets/fonts/PlusJakartaSans-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
